services:
  fastapi-spamassassin:
    build: .
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - ALLOWED_ORIGINS=http://localhost:3000
      - APP_VERSION=v1.0.0b1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"