import re
import time
import random
from datetime import datetime
from typing import Tuple, Dict

from schema import EmailMessage


def detect_email_headers(content: str) -> bool:
    """
    Detect if the email content already contains headers.

    :param content: Raw email content to analyze
    :return: True if headers are detected, False otherwise
    """
    lines = content.strip().split('\n')
    if not lines:
        return False

    # Check for common email headers at the beginning of the content
    header_patterns = [
        r'^(From|To|Subject|Date|Message-ID|Reply-To|Cc|Bcc|MIME-Version|Content-Type|Content-Transfer-Encoding|Received|Return-Path|X-.*):',
        r'^[A-Za-z-]+:\s*.*'  # Generic header pattern
    ]

    # Check first few lines for header patterns
    header_count = 0
    for line in lines[:20]:  # Check first 20 lines
        line = line.strip()
        if not line:  # Empty line indicates end of headers
            break

        # Check if line matches header pattern
        for pattern in header_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                header_count += 1
                break
        else:
            # If we've found some headers and hit a non-header line,
            # this might be a folded header or end of headers
            if header_count > 0 and not line.startswith((' ', '\t')):
                break

    # Consider it has headers if we found at least 2 header-like lines
    return header_count >= 2


def extract_headers_and_body(content: str) -> Tuple[Dict[str, str], str]:
    """
    Extract headers and body from raw email content.

    :param content: Raw email content with headers
    :return: Tuple of (headers_dict, body_content)
    """
    lines = content.split('\n')
    headers = {}
    body_start_index = 0
    current_header = None
    current_value = ""

    for i, line in enumerate(lines):
        # Empty line indicates end of headers
        if not line.strip():
            body_start_index = i + 1
            break

        # Check if this is a new header or continuation of previous header
        if line.startswith((' ', '\t')):
            # Continuation of previous header (folded header)
            if current_header:
                current_value += " " + line.strip()
        else:
            # Save previous header if exists
            if current_header:
                headers[current_header] = current_value.strip()

            # Parse new header
            if ':' in line:
                header_name, header_value = line.split(':', 1)
                current_header = header_name.strip()
                current_value = header_value.strip()
            else:
                current_header = None
                current_value = ""

    # Save the last header
    if current_header:
        headers[current_header] = current_value.strip()

    # Extract body content
    body = '\n'.join(lines[body_start_index:]).strip()

    return headers, body


def parse_spamc_output(spamc_output: str) -> dict:
    """
    Parse comprehensive spamc output into structured data
    :param spamc_output: Raw output string from spamc command
    """
    result = {
        'headers': {},
        'spam_info': {},
        'content_analysis': [],
        'original_message': '',
        'raw_output': spamc_output
    }
    
    lines = spamc_output.split('\n')
    
    # Parse spam headers
    for line in lines:
        if line.startswith('X-Spam-'):
            if ':' in line:
                key, value = line.split(':', 1)
                result['headers'][key.strip()] = value.strip()
    
    # Extract key spam information
    spam_status_match = re.search(r'X-Spam-Status:\s*(\w+),\s*score=([\d.-]+)\s*required=([\d.-]+)\s*tests=([A-Z_,\s]+)', spamc_output, re.MULTILINE)
    if spam_status_match:
        result['spam_info'] = {
            'status': spam_status_match.group(1),
            'score': float(spam_status_match.group(2)),
            'spamassassin_threshold': float(spam_status_match.group(3)),
            'tests': [test.strip() for test in spam_status_match.group(4).replace('\n\t', '').split(',') if test.strip()],
            'is_spam': spam_status_match.group(1).lower() == 'yes'
        }
    
    # Parse content analysis details (the detailed scoring table)
    analysis_match = re.search(r'Content analysis details:.*?\n\n pts rule name.*?\n---- .*? ----.*?\n(.*?)(?=\n\n|\n------------)', spamc_output, re.DOTALL)
    if analysis_match:
        analysis_lines = analysis_match.group(1).strip().split('\n')
        for line in analysis_lines:
            if line.strip():
                # Parse each rule line: score, rule_name, description
                rule_match = re.match(r'\s*([\d.-]+)\s+([A-Z_]+)\s+(.*)', line)
                if rule_match:
                    result['content_analysis'].append({
                        'points': float(rule_match.group(1)),
                        'rule_name': rule_match.group(2),
                        'description': rule_match.group(3).strip()
                    })
    
    # Extract original message
    original_match = re.search(r'Content-Transfer-Encoding: 8bit\n\n(.*)', spamc_output, re.DOTALL)
    if original_match:
        result['original_message'] = original_match.group(1).strip()
    
    return result


def build_email_with_headers(email: EmailMessage) -> str:
    """
    Build a properly formatted email with headers to prevent MISSING_* spam rules.
    Auto-detects if headers are already present in the email content.

    :param email: EmailMessage object containing content and optional headers
    :return: Properly formatted email with headers
    """
    # Check if headers are already present in the email content
    if detect_email_headers(email.content):
        # Headers are already present, extract them and use the existing structure
        existing_headers, body_content = extract_headers_and_body(email.content)

        # If EmailMessage object has header fields provided, they take precedence
        # over the extracted headers for key fields
        updated_headers = existing_headers.copy()

        # Override with EmailMessage fields if provided
        if email.subject:
            updated_headers['Subject'] = email.subject
        if email.from_email:
            updated_headers['From'] = email.from_email
        if email.to_email:
            updated_headers['To'] = email.to_email
        if email.date:
            updated_headers['Date'] = email.date
        if email.message_id:
            updated_headers['Message-ID'] = email.message_id
        if email.reply_to:
            updated_headers['Reply-To'] = email.reply_to
        if email.cc:
            updated_headers['Cc'] = email.cc
        if email.bcc:
            updated_headers['Bcc'] = email.bcc

        # Ensure required headers are present
        if 'Date' not in updated_headers:
            current_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S %z")
            if not current_date.endswith((' +0000', ' -0000')):
                current_date += " +0000"
            updated_headers['Date'] = current_date

        if 'From' not in updated_headers:
            updated_headers['From'] = "<EMAIL>"

        if 'To' not in updated_headers:
            updated_headers['To'] = "<EMAIL>"

        if 'Subject' not in updated_headers:
            updated_headers['Subject'] = "No Subject"

        if 'Message-ID' not in updated_headers:
            msg_id = f"<{int(time.time())}.{random.randint(1000, 9999)}@example.com>"
            updated_headers['Message-ID'] = msg_id

        # Ensure MIME headers are present
        if 'MIME-Version' not in updated_headers:
            updated_headers['MIME-Version'] = "1.0"
        if 'Content-Type' not in updated_headers:
            updated_headers['Content-Type'] = "text/plain; charset=utf-8"
        if 'Content-Transfer-Encoding' not in updated_headers:
            updated_headers['Content-Transfer-Encoding'] = "8bit"

        # Rebuild email with updated headers
        header_lines = []
        for header_name, header_value in updated_headers.items():
            header_lines.append(f"{header_name}: {header_value}")

        return "\n".join(header_lines) + "\n\n" + body_content

    else:
        # No headers detected, build email with headers from EmailMessage fields
        headers = []

        # Add Date header (required to prevent MISSING_DATE)
        if email.date:
            headers.append(f"Date: {email.date}")
        else:
            # Generate current date in RFC 2822 format
            current_date = datetime.now().strftime("%a, %d %b %Y %H:%M:%S %z")
            if not current_date.endswith((' +0000', ' -0000')):  # Add timezone if missing
                current_date += " +0000"
            headers.append(f"Date: {current_date}")

        # Add From header (required to prevent MISSING_FROM)
        if email.from_email:
            headers.append(f"From: {email.from_email}")
        else:
            headers.append("From: <EMAIL>")

        # Add To header (required to prevent MISSING_HEADERS)
        if email.to_email:
            headers.append(f"To: {email.to_email}")
        else:
            headers.append("To: <EMAIL>")

        # Add Subject header (required to prevent MISSING_SUBJECT)
        if email.subject:
            headers.append(f"Subject: {email.subject}")
        else:
            headers.append("Subject: No Subject")

        # Add Message-ID header (required to prevent MISSING_MID)
        if email.message_id:
            headers.append(f"Message-ID: {email.message_id}")
        else:
            # Generate a basic message ID
            msg_id = f"<{int(time.time())}.{random.randint(1000, 9999)}@example.com>"
            headers.append(f"Message-ID: {msg_id}")

        # Add optional headers if provided
        if email.reply_to:
            headers.append(f"Reply-To: {email.reply_to}")

        if email.cc:
            headers.append(f"Cc: {email.cc}")

        if email.bcc:
            headers.append(f"Bcc: {email.bcc}")

        # Add MIME version to prevent some MIME-related spam rules
        headers.append("MIME-Version: 1.0")
        headers.append("Content-Type: text/plain; charset=utf-8")
        headers.append("Content-Transfer-Encoding: 8bit")

        # Combine headers with content (blank line separates headers from body)
        email_with_headers = "\n".join(headers) + "\n\n" + email.content.strip()

        return email_with_headers
