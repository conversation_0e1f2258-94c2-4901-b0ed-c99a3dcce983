# Multi-stage build for production optimization
FROM ubuntu:22.04 as base

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies and build tools for SpamAssassin compilation
RUN apt-get update && apt-get install -y \
    build-essential \
    perl \
    libdigest-sha-perl \
    libnetaddr-ip-perl \
    libnet-dns-perl \
    libmail-spf-perl \
    libmail-dkim-perl \
    libio-string-perl \
    libio-socket-ssl-perl \
    libio-socket-inet6-perl \
    libarchive-tar-perl \
    libhtml-parser-perl \
    libdbi-perl \
    libnet-ident-perl \
    libencoding-fixlatin-perl \
    libmime-base64-perl \
    libtime-hires-perl \
    libencode-detect-perl \
    cpanminus \
    wget \
    gnupg \
    python3 \
    python3-pip \
    python3-venv \
    supervisor \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# Install additional Perl modules via CPAN that aren't available as packages
RUN cpanm --notest \
    Geo::IP \
    IP::Country

# Download and install SpamAssassin 4.0.2 from source
RUN cd /tmp \
    && wget https://archive.apache.org/dist/spamassassin/source/Mail-SpamAssassin-4.0.2.tar.gz \
    && wget https://archive.apache.org/dist/spamassassin/source/Mail-SpamAssassin-4.0.2.tar.gz.asc \
    && tar -xzf Mail-SpamAssassin-4.0.2.tar.gz \
    && cd Mail-SpamAssassin-4.0.2 \
    && perl Makefile.PL \
    && make \
    && make install \
    && cd / \
    && rm -rf /tmp/Mail-SpamAssassin-4.0.2*

# Create non-root user for security
RUN groupadd -r deliveryman-spamapp && useradd -r -g deliveryman-spamapp -d /app -s /bin/bash deliveryman-spamapp

# Create app directory with proper permissions
WORKDIR /app
RUN chown -R deliveryman-spamapp:deliveryman-spamapp /app

# Python build stage
FROM base as python-deps

# Copy requirements first for better caching
COPY requirements.txt .

# Create virtual environment and install Python dependencies
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt

# Production stage
FROM base as production

# Copy virtual environment from build stage
COPY --from=python-deps /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:/usr/local/bin:$PATH"

# Copy application code
COPY --chown=deliveryman-spamapp:deliveryman-spamapp . /app/

# Configure SpamAssassin directories with proper permissions
RUN mkdir -p /var/lib/spamassassin \
    /var/run/spamd \
    /var/log/supervisor \
    && chown -R deliveryman-spamapp:deliveryman-spamapp /var/lib/spamassassin /var/run/spamd \
    && chmod 755 /var/lib/spamassassin /var/run/spamd

# Update SpamAssassin rules and ensure proper permissions
RUN /usr/local/bin/sa-update --no-gpg || true

# Create supervisor configuration
RUN echo '[supervisord]\n\
    nodaemon=true\n\
    user=root\n\
    logfile=/var/log/supervisor/supervisord.log\n\
    pidfile=/var/run/supervisord.pid\n\
    \n\
    [program:spamd]\n\
    command=/usr/local/bin/spamd --nouser-config --allowed-ips=0.0.0.0/0 --max-children=5 --pidfile=/var/run/spamd/spamd.pid\n\
    user=deliveryman-spamapp\n\
    autostart=true\n\
    autorestart=true\n\
    stderr_logfile=/var/log/supervisor/spamd.err.log\n\
    stdout_logfile=/var/log/supervisor/spamd.out.log\n\
    \n\
    [program:fastapi]\n\
    command=/opt/venv/bin/uvicorn app:app --host 0.0.0.0 --port 8000 --workers 1\n\
    directory=/app\n\
    user=deliveryman-spamapp\n\
    autostart=true\n\
    autorestart=true\n\
    stderr_logfile=/var/log/supervisor/fastapi.err.log\n\
    stdout_logfile=/var/log/supervisor/fastapi.out.log' > /etc/supervisor/conf.d/supervisord.conf

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# NOTE: We run supervisord as root so it can manage processes and drop privileges
# The individual programs (spamd and fastapi) run as deliveryman-spamapp user
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf"]