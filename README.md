# FastAPI SpamAssassin API

A FastAPI service that integrates with SpamAssassin for email spam detection.

## Quick Start

### Development

1. **Clone and setup:**
   ```bash
   git clone https://gitlab.com/aminmemon/spam-assassin-api
   cd spam-assassin-api
   ```

2. **Run locally with Docker Compose:**
   ```bash
   # Development mode (builds locally)
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Test the service:**
   ```bash
   # Health check
   curl http://localhost:8080/health
   
   # Spam check
   curl -X POST http://localhost:8080/check-spam \
     -H "Content-Type: application/json" \
     -d '{"email_content": "Your email content here"}'
   ```

### Production Deployment

1. **Build and push image:**
   ```bash
   # Login to GitLab Container Registry
   docker login registry.gitlab.com
   
   # Build and push (will prompt for version)
   ./build-and-push.sh
   ```

2. **Deploy:**
   ```bash
   # Quick deployment
   ./deploy.sh
   
   # Or manual deployment
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Test production deployment:**
   ```bash
   curl https://spamcheck.deliveryman.ai/health
   ```

## API Endpoints

- `GET /health` - Health check endpoint
- `POST /check-spam` - Spam detection endpoint
- `GET /docs` - Interactive API documentation (Swagger UI)

## Configuration

### Environment Variables

- `PORT` - Server port (default: 8000)
- `ALLOWED_ORIGINS` - CORS allowed origins

### Docker Compose Services

- **fastapi-spamassassin** - Main application service
- **nginx** - Reverse proxy with SSL termination

## Monitoring

### Health Checks
- FastAPI: `http://localhost:8000/health`
- Production: `https://spamcheck.deliveryman.ai/health`

### Logs
```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f fastapi-spamassassin
docker-compose -f docker-compose.prod.yml logs -f nginx
```

## Updating

To deploy a new version:

1. Update your code
2. Run `./build-and-push.sh` with a new version number
3. Update the image tag in `docker-compose.prod.yml`
4. Run `./deploy.sh`
