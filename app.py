import os
import logging
import subprocess

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from schema import EmailMessage, SpamResponse
from utils import parse_spamc_output, build_email_with_headers

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

app = FastAPI(
    title="Deliveryman.ai SpamAssassin",
    description="Deliveryman.ai SpamAssassin API service for email spam detection",
    version=os.getenv("VERSION"),
)

# Add CORS middleware for production use
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.environ["ALLOWED_ORIGINS"].split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)


@app.post("/check", response_model=SpamResponse)
async def check_spam(email: EmailMessage):
    """
    Analyze email content for spam using SpamAssassin.
    Returns spam classification, score, and detailed report.
    """
    try:
        logger.info(f"Processing spam check request with threshold {email.threshold}")
        
        # Validate email content
        if not email.content.strip():
            raise HTTPException(status_code=400, detail="Email content cannot be empty")
        
        if len(email.content) > 10 * 1024 * 1024:  # 10MB limit
            raise HTTPException(status_code=400, detail="Email content too large (max 10MB)")
        
        # Build properly formatted email with headers
        formatted_email = build_email_with_headers(email)

        # Run SpamAssassin check with timeout
        result = subprocess.run(
            ['spamc'],
            input=formatted_email,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )

        # Check for SpamAssassin errors
        if result.returncode != 0:
            logger.warning(f"SpamAssassin returned non-zero exit code: {result.returncode}")

            if result.stderr:
                logger.warning(f"SpamAssassin stderr: {result.stderr}")

            raise HTTPException(status_code=503, detail="SpamAssassin service error")

        parsed_result = parse_spamc_output(result.stdout)

        # Parse spam score from result
        try:
            spam_score = float(parsed_result['spam_info']['score'])
        except (ValueError, KeyError) as e:
            logger.warning(f"Failed to parse spam score: {e}, attempting fallback")
            raise HTTPException(status_code=500, detail="Failed to parse spam score")

        is_spam = spam_score >= email.threshold
        
        logger.info(f"Spam check completed: score={spam_score}, is_spam={is_spam}")
        
        return SpamResponse(
            is_spam=is_spam,
            spam_score=spam_score,
            report=parsed_result['original_message'] or "No detailed report available",
            threshold_used=email.threshold,
            parsed_output=parsed_result
        )

    except subprocess.TimeoutExpired:
        logger.error("SpamAssassin check timed out")
        raise HTTPException(status_code=408, detail="Spam check request timed out")
    
    except subprocess.CalledProcessError as e:
        logger.error(f"SpamAssassin process error: {e}")
        raise HTTPException(status_code=503, detail="SpamAssassin service error")
    
    except Exception as e:
        logger.error(f"Unexpected error during spam check: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/health")
async def health_check():
    """
    Comprehensive health check endpoint.
    Verifies SpamAssassin daemon status and system readiness.
    """
    try:
        # Check if SpamAssassin daemon is running
        spamd_check = subprocess.run(
            ['pgrep', 'spamd'], 
            capture_output=True, 
            timeout=5
        )

        spamd_running = spamd_check.returncode == 0

        # Test SpamAssassin functionality with a simple test
        email_content = """Subject: Test Email
From: <EMAIL>
To: <EMAIL>

This is a test email content."""

        test_result = subprocess.run(
            ['spamc'],
            input=email_content,
            capture_output=True,
            text=True,
            timeout=10
        )

        spamc_working = test_result.returncode == 0
        
        status = "healthy" if (spamd_running and spamc_working) else "unhealthy"
        
        health_data = {
            "status": status,
            "spamd_running": spamd_running,
            "spamc_working": spamc_working,
            "version": os.getenv("VERSION")
        }
        
        if status == "unhealthy":
            raise HTTPException(status_code=503, detail=health_data)
        
        return health_data
        
    except subprocess.TimeoutExpired:
        logger.error("Health check timed out")
        raise HTTPException(status_code=503, detail={"status": "timeout", "message": "Health check timed out"})
    
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail={"status": "error", "message": str(e)})


@app.get("/")
async def root():
    """
    API information endpoint.
    """
    return {
        "service": "Deliveryman.ai SpamAssassin",
        "version": os.getenv("VERSION"),
        "status": "running",
        "endpoints": {
            "spam_check": "/check",
            "health": "/health",
            "docs": "/docs",
        }
    }


if __name__ == "__main__":
    # Development server configuration
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        log_level="info",
        access_log=True
    )
