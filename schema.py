from typing import Optional
from pydantic import BaseModel, Field


class EmailMessage(BaseModel):
    content: str = Field(..., description="Email content to analyze for spam")
    threshold: float = Field(
        default=5.0,
        ge=0.0,
        le=50.0,
        description="Spam score threshold (0-50)"
    )
    # Optional email headers to prevent MISSING_* spam rules
    subject: Optional[str] = Field(None, description="Email subject line")
    from_email: Optional[str] = Field(None, alias="from", description="Sender email address")
    to_email: Optional[str] = Field(None, alias="to", description="Recipient email address")
    date: Optional[str] = Field(None, description="Email date (RFC 2822 format)")
    message_id: Optional[str] = Field(None, description="Unique message identifier")
    reply_to: Optional[str] = Field(None, description="Reply-to email address")
    cc: Optional[str] = Field(None, description="CC recipients")
    bcc: Optional[str] = Field(None, description="BCC recipients")


class SpamResponse(BaseModel):
    is_spam: bool = Field(description="Whether the email is classified as spam")
    spam_score: float = Field(description="Numerical spam score from SpamAssassin")
    report: str = Field(description="Detailed SpamAssassin analysis report")
    threshold_used: float = Field(description="Threshold value used for classification")
    parsed_output: dict = Field(default_factory=dict, description="SpamAssassin parsed output")
